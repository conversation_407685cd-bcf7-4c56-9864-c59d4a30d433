<?php

namespace App\Services;

use App\Helpers\PdfHelper;
use App\Models\WorkplanActivityModel;
use App\Models\WorkplanModel;
use App\Models\ProposalModel;
use App\Models\WorkplanTrainingActivityModel;
use App\Models\WorkplanInputActivityModel;
use App\Models\WorkplanInfrastructureActivityModel;
use App\Models\WorkplanOutputActivityModel;
use App\Models\BranchesModel;
use App\Models\UserModel;
use App\Models\GovStructureModel;

/**
 * PDF Service Class
 * 
 * Handles PDF generation for activities and reports in the AMIS system
 */
class PdfService
{
    private $workplanActivityModel;
    private $workplanModel;
    private $proposalModel;
    private $branchModel;
    private $userModel;
    private $govStructureModel;

    public function __construct()
    {
        $this->workplanActivityModel = new WorkplanActivityModel();
        $this->workplanModel = new WorkplanModel();
        $this->proposalModel = new ProposalModel();
        $this->branchModel = new BranchesModel();
        $this->userModel = new UserModel();
        $this->govStructureModel = new GovStructureModel();
    }

    /**
     * Generate PDF for activity details
     * 
     * @param int $activityId Activity ID
     * @return string PDF output
     */
    public function generateActivityPdf($activityId)
    {
        // Get activity details with all related data
        $activity = $this->getActivityDetails($activityId);
        
        if (!$activity) {
            throw new \Exception('Activity not found');
        }

        // Create PDF
        $pdf = new PdfHelper('Activity Report - ' . $activity['title'], 'P');
        $pdf->addPage();

        // Add activity header information
        $this->addActivityHeader($pdf, $activity);

        // Add activity details section
        $this->addActivityDetails($pdf, $activity);

        // Add implementation details if available
        $this->addImplementationDetails($pdf, $activity);

        // Add plan links section
        $this->addPlanLinks($pdf, $activity);

        // Add proposal information if available
        $this->addProposalInformation($pdf, $activity);

        return $pdf->output('Activity_Report_' . $activityId . '.pdf', 'I');
    }

    /**
     * Get comprehensive activity details
     * 
     * @param int $activityId
     * @return array|null
     */
    private function getActivityDetails($activityId)
    {
        $activity = $this->workplanActivityModel
            ->select('workplan_activities.*, 
                     workplans.title as workplan_title,
                     workplans.year as workplan_year,
                     branches.name as branch_name,
                     CONCAT(supervisors.fname, " ", supervisors.lname) as supervisor_name,
                     supervisors.email as supervisor_email')
            ->join('workplans', 'workplans.id = workplan_activities.workplan_id', 'left')
            ->join('branches', 'branches.id = workplan_activities.branch_id', 'left')
            ->join('users as supervisors', 'supervisors.id = workplan_activities.supervisor_id', 'left')
            ->where('workplan_activities.id', $activityId)
            ->first();

        if (!$activity) {
            return null;
        }

        // Get proposal information
        $proposal = $this->proposalModel
            ->select('proposal.*, 
                     CONCAT(officers.fname, " ", officers.lname) as officer_name,
                     officers.email as officer_email,
                     provinces.name as province_name,
                     districts.name as district_name,
                     llgs.name as llg_name,
                     wards.name as ward_name')
            ->join('users as officers', 'officers.id = proposal.action_officer_id', 'left')
            ->join('gov_structure as provinces', 'provinces.id = proposal.province_id', 'left')
            ->join('gov_structure as districts', 'districts.id = proposal.district_id', 'left')
            ->join('gov_structure as llgs', 'llgs.id = proposal.llg_id', 'left')
            ->join('gov_structure as wards', 'wards.id = proposal.ward_id', 'left')
            ->where('proposal.activity_id', $activityId)
            ->first();

        $activity['proposal'] = $proposal;

        // Get implementation details based on activity type
        $activity['implementation'] = $this->getImplementationDetails($activityId, $activity['activity_type']);

        return $activity;
    }

    /**
     * Get implementation details based on activity type
     * 
     * @param int $activityId
     * @param string $activityType
     * @return array|null
     */
    private function getImplementationDetails($activityId, $activityType)
    {
        switch ($activityType) {
            case 'training':
                $model = new WorkplanTrainingActivityModel();
                return $model->where('activity_id', $activityId)->first();
                
            case 'inputs':
                $model = new WorkplanInputActivityModel();
                return $model->where('activity_id', $activityId)->first();
                
            case 'infrastructure':
                $model = new WorkplanInfrastructureActivityModel();
                return $model->where('activity_id', $activityId)->first();
                
            case 'output':
                $model = new WorkplanOutputActivityModel();
                return $model->where('activity_id', $activityId)->first();
                
            default:
                return null;
        }
    }

    /**
     * Add activity header information to PDF
     * 
     * @param PdfHelper $pdf
     * @param array $activity
     */
    private function addActivityHeader($pdf, $activity)
    {
        $pdf->addTitle('Activity Report', 18);
        
        // Activity basic information table
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #f0f0f0;">
                <td style="width: 25%; font-weight: bold;">Activity Title:</td>
                <td style="width: 75%;">' . htmlspecialchars($activity['title']) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Activity Type:</td>
                <td>' . ucfirst($activity['activity_type']) . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Workplan:</td>
                <td>' . htmlspecialchars($activity['workplan_title']) . ' (' . $activity['workplan_year'] . ')</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Branch:</td>
                <td>' . htmlspecialchars($activity['branch_name'] ?? 'Not specified') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Supervisor:</td>
                <td>' . htmlspecialchars($activity['supervisor_name'] ?? 'Not assigned') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Created Date:</td>
                <td>' . date('F j, Y', strtotime($activity['created_at'])) . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
        $pdf->addLineBreak(10);
    }

    /**
     * Add activity details section
     * 
     * @param PdfHelper $pdf
     * @param array $activity
     */
    private function addActivityDetails($pdf, $activity)
    {
        $pdf->addSubtitle('Activity Description', 14);
        
        $description = !empty($activity['description']) ? $activity['description'] : 'No description provided.';
        $pdf->addText($description, 10);
        $pdf->addLineBreak(10);
    }

    /**
     * Add implementation details section
     * 
     * @param PdfHelper $pdf
     * @param array $activity
     */
    private function addImplementationDetails($pdf, $activity)
    {
        if (!$activity['implementation']) {
            return;
        }

        $pdf->addSubtitle('Implementation Details', 14);
        
        $implementation = $activity['implementation'];
        
        switch ($activity['activity_type']) {
            case 'training':
                $this->addTrainingImplementation($pdf, $implementation);
                break;
                
            case 'inputs':
                $this->addInputImplementation($pdf, $implementation);
                break;
                
            case 'infrastructure':
                $this->addInfrastructureImplementation($pdf, $implementation);
                break;
                
            case 'output':
                $this->addOutputImplementation($pdf, $implementation);
                break;
        }
        
        $pdf->addLineBreak(10);
    }

    /**
     * Add training implementation details
     * 
     * @param PdfHelper $pdf
     * @param array $implementation
     */
    private function addTrainingImplementation($pdf, $implementation)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Trainers:</td>
                <td style="width: 75%;">' . htmlspecialchars($implementation['trainers'] ?? 'Not specified') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Topics:</td>
                <td>' . htmlspecialchars($implementation['topics'] ?? 'Not specified') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">GPS Coordinates:</td>
                <td>' . htmlspecialchars($implementation['gps_coordinates'] ?? 'Not provided') . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
        
        // Add trainees information if available
        if (!empty($implementation['trainees'])) {
            $trainees = json_decode($implementation['trainees'], true);
            if (is_array($trainees) && count($trainees) > 0) {
                $pdf->addLineBreak(5);
                $pdf->addSubtitle('Trainees Information', 12);
                
                $headers = ['Name', 'Gender', 'Age', 'Contact'];
                $data = [];
                
                foreach ($trainees as $trainee) {
                    $data[] = [
                        $trainee['name'] ?? '',
                        $trainee['gender'] ?? '',
                        $trainee['age'] ?? '',
                        $trainee['contact'] ?? ''
                    ];
                }
                
                $pdf->addTable($headers, $data);
            }
        }
    }

    /**
     * Add input implementation details
     * 
     * @param PdfHelper $pdf
     * @param array $implementation
     */
    private function addInputImplementation($pdf, $implementation)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Distribution Date:</td>
                <td style="width: 75%;">' . ($implementation['distribution_date'] ? date('F j, Y', strtotime($implementation['distribution_date'])) : 'Not specified') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Distribution Location:</td>
                <td>' . htmlspecialchars($implementation['distribution_location'] ?? 'Not specified') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Total Value:</td>
                <td>K ' . number_format($implementation['total_value'] ?? 0, 2) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">GPS Coordinates:</td>
                <td>' . htmlspecialchars($implementation['gps_coordinates'] ?? 'Not provided') . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
    }

    /**
     * Add infrastructure implementation details
     * 
     * @param PdfHelper $pdf
     * @param array $implementation
     */
    private function addInfrastructureImplementation($pdf, $implementation)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Construction Date:</td>
                <td style="width: 75%;">' . ($implementation['construction_date'] ? date('F j, Y', strtotime($implementation['construction_date'])) : 'Not specified') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Construction Location:</td>
                <td>' . htmlspecialchars($implementation['construction_location'] ?? 'Not specified') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Total Value:</td>
                <td>K ' . number_format($implementation['total_value'] ?? 0, 2) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">GPS Coordinates:</td>
                <td>' . htmlspecialchars($implementation['gps_coordinates'] ?? 'Not provided') . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
    }

    /**
     * Add output implementation details
     * 
     * @param PdfHelper $pdf
     * @param array $implementation
     */
    private function addOutputImplementation($pdf, $implementation)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Delivery Date:</td>
                <td style="width: 75%;">' . ($implementation['delivery_date'] ? date('F j, Y', strtotime($implementation['delivery_date'])) : 'Not specified') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Delivery Location:</td>
                <td>' . htmlspecialchars($implementation['delivery_location'] ?? 'Not specified') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Total Value:</td>
                <td>K ' . number_format($implementation['total_value'] ?? 0, 2) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">GPS Coordinates:</td>
                <td>' . htmlspecialchars($implementation['gps_coordinates'] ?? 'Not provided') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Remarks:</td>
                <td>' . htmlspecialchars($implementation['remarks'] ?? 'No remarks') . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
    }

    /**
     * Add plan links section
     * 
     * @param PdfHelper $pdf
     * @param array $activity
     */
    private function addPlanLinks($pdf, $activity)
    {
        $pdf->addSubtitle('Plan Links', 14);
        $pdf->addText('This section shows how this activity is linked to various development plans.', 10);
        $pdf->addLineBreak(5);
        
        // Note: Plan links implementation would require additional queries
        // This is a placeholder for the plan links section
        $pdf->addText('Plan links information will be displayed here.', 10);
        $pdf->addLineBreak(10);
    }

    /**
     * Add proposal information section
     * 
     * @param PdfHelper $pdf
     * @param array $activity
     */
    private function addProposalInformation($pdf, $activity)
    {
        if (!$activity['proposal']) {
            return;
        }

        $pdf->addSubtitle('Proposal Information', 14);
        
        $proposal = $activity['proposal'];
        
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #f0f8ff;">
                <td style="width: 25%; font-weight: bold;">Action Officer:</td>
                <td style="width: 75%;">' . htmlspecialchars($proposal['officer_name'] ?? 'Not assigned') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Status:</td>
                <td>' . ucfirst($proposal['status'] ?? 'pending') . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Location:</td>
                <td>' . htmlspecialchars($proposal['province_name'] ?? '') . 
                    ($proposal['district_name'] ? ', ' . $proposal['district_name'] : '') .
                    ($proposal['llg_name'] ? ', ' . $proposal['llg_name'] : '') .
                    ($proposal['ward_name'] ? ', ' . $proposal['ward_name'] : '') . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Estimated Cost:</td>
                <td>K ' . number_format($proposal['estimated_cost'] ?? 0, 2) . '</td>
            </tr>
            <tr style="background-color: #f9f9f9;">
                <td style="font-weight: bold;">Created Date:</td>
                <td>' . date('F j, Y', strtotime($proposal['created_at'])) . '</td>
            </tr>
        </table>';
        
        $pdf->addHtml($html);
    }

    /**
     * Generate PDF for reports with charts and data
     *
     * @param string $reportType Type of report (workplan, nasp, mtdp, commodity, activity-maps)
     * @param array $data Report data
     * @param array $chartData Chart data for visualization
     * @return string PDF output
     */
    public function generateReportPdf($reportType, $data, $chartData = [])
    {
        $title = $this->getReportTitle($reportType);

        // Create PDF in landscape for better chart display
        $pdf = new PdfHelper($title, 'L');
        $pdf->addPage();

        // Add report header
        $pdf->addTitle($title, 18);
        $pdf->addText('Generated on: ' . date('F j, Y \a\t g:i A'), 10, 'R');
        $pdf->addLineBreak(10);

        // Add report summary
        $this->addReportSummary($pdf, $reportType, $data, $chartData);

        // Add detailed data tables
        $this->addReportTables($pdf, $reportType, $data);

        return $pdf->output($reportType . '_Report_' . date('Y-m-d') . '.pdf', 'I');
    }

    /**
     * Get report title based on type
     *
     * @param string $reportType
     * @return string
     */
    private function getReportTitle($reportType)
    {
        $titles = [
            'workplan' => 'Workplan Reports',
            'nasp' => 'NASP Plans Report',
            'mtdp' => 'MTDP Plans Report',
            'commodity' => 'Commodity Production Report',
            'activity-maps' => 'Activity Maps Report'
        ];

        return $titles[$reportType] ?? 'System Report';
    }

    /**
     * Add report summary section
     *
     * @param PdfHelper $pdf
     * @param string $reportType
     * @param array $data
     * @param array $chartData
     */
    private function addReportSummary($pdf, $reportType, $data, $chartData)
    {
        $pdf->addSubtitle('Report Summary', 14);

        switch ($reportType) {
            case 'workplan':
                $this->addWorkplanSummary($pdf, $data, $chartData);
                break;
            case 'nasp':
                $this->addNaspSummary($pdf, $data, $chartData);
                break;
            case 'mtdp':
                $this->addMtdpSummary($pdf, $data, $chartData);
                break;
            case 'commodity':
                $this->addCommoditySummary($pdf, $data, $chartData);
                break;
            case 'activity-maps':
                $this->addActivityMapsSummary($pdf, $data, $chartData);
                break;
        }

        $pdf->addLineBreak(10);
    }

    /**
     * Add workplan summary
     *
     * @param PdfHelper $pdf
     * @param array $data
     * @param array $chartData
     */
    private function addWorkplanSummary($pdf, $data, $chartData)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Total Workplans:</td>
                <td style="width: 25%;">' . count($data['workplans'] ?? []) . '</td>
                <td style="width: 25%; font-weight: bold;">Total Activities:</td>
                <td style="width: 25%;">' . count($data['activities'] ?? []) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Total Proposals:</td>
                <td>' . count($data['proposals'] ?? []) . '</td>
                <td style="font-weight: bold;">Total Budget:</td>
                <td>K ' . number_format($chartData['totalCost'] ?? 0, 2) . '</td>
            </tr>
        </table>';

        $pdf->addHtml($html);
    }

    /**
     * Add NASP summary
     *
     * @param PdfHelper $pdf
     * @param array $data
     * @param array $chartData
     */
    private function addNaspSummary($pdf, $data, $chartData)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 20%; font-weight: bold;">Plans:</td>
                <td style="width: 15%;">' . count($data['plans'] ?? []) . '</td>
                <td style="width: 20%; font-weight: bold;">APAs:</td>
                <td style="width: 15%;">' . count($data['apas'] ?? []) . '</td>
                <td style="width: 20%; font-weight: bold;">DIPs:</td>
                <td style="width: 10%;">' . count($data['dips'] ?? []) . '</td>
            </tr>
            <tr>
                <td style="font-weight: bold;">Specific Areas:</td>
                <td>' . count($data['specificAreas'] ?? []) . '</td>
                <td style="font-weight: bold;">Outputs:</td>
                <td>' . count($data['outputs'] ?? []) . '</td>
                <td style="font-weight: bold;">Indicators:</td>
                <td>' . count($data['indicators'] ?? []) . '</td>
            </tr>
        </table>';

        $pdf->addHtml($html);
    }

    /**
     * Add MTDP summary
     *
     * @param PdfHelper $pdf
     * @param array $data
     * @param array $chartData
     */
    private function addMtdpSummary($pdf, $data, $chartData)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 20%; font-weight: bold;">Plans:</td>
                <td style="width: 15%;">' . count($data['plans'] ?? []) . '</td>
                <td style="width: 20%; font-weight: bold;">SPAs:</td>
                <td style="width: 15%;">' . count($data['spas'] ?? []) . '</td>
                <td style="width: 20%; font-weight: bold;">DIPs:</td>
                <td style="width: 10%;">' . count($data['dips'] ?? []) . '</td>
            </tr>
        </table>';

        $pdf->addHtml($html);
    }

    /**
     * Add commodity summary
     *
     * @param PdfHelper $pdf
     * @param array $data
     * @param array $chartData
     */
    private function addCommoditySummary($pdf, $data, $chartData)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Total Productions:</td>
                <td style="width: 25%;">' . count($data['productions'] ?? []) . '</td>
                <td style="width: 25%; font-weight: bold;">Total Commodities:</td>
                <td style="width: 25%;">' . count($data['commodities'] ?? []) . '</td>
            </tr>
        </table>';

        $pdf->addHtml($html);
    }

    /**
     * Add activity maps summary
     *
     * @param PdfHelper $pdf
     * @param array $data
     * @param array $chartData
     */
    private function addActivityMapsSummary($pdf, $data, $chartData)
    {
        $html = '<table border="1" cellpadding="5" cellspacing="0" style="width: 100%;">
            <tr style="background-color: #e8f4fd;">
                <td style="width: 25%; font-weight: bold;">Total Activities:</td>
                <td style="width: 25%;">' . count($data['activities'] ?? []) . '</td>
                <td style="width: 25%; font-weight: bold;">With GPS Coordinates:</td>
                <td style="width: 25%;">' . count(array_filter($data['activities'] ?? [], function($a) { return !empty($a['coordinates']); })) . '</td>
            </tr>
        </table>';

        $pdf->addHtml($html);
    }

    /**
     * Add detailed report tables
     *
     * @param PdfHelper $pdf
     * @param string $reportType
     * @param array $data
     */
    private function addReportTables($pdf, $reportType, $data)
    {
        $pdf->addSubtitle('Detailed Data', 14);

        switch ($reportType) {
            case 'workplan':
                $this->addWorkplanTables($pdf, $data);
                break;
            case 'nasp':
                $this->addNaspTables($pdf, $data);
                break;
            case 'mtdp':
                $this->addMtdpTables($pdf, $data);
                break;
            case 'commodity':
                $this->addCommodityTables($pdf, $data);
                break;
            case 'activity-maps':
                $this->addActivityMapsTables($pdf, $data);
                break;
        }
    }

    /**
     * Add workplan tables
     *
     * @param PdfHelper $pdf
     * @param array $data
     */
    private function addWorkplanTables($pdf, $data)
    {
        if (!empty($data['workplans'])) {
            $pdf->addSubtitle('Workplans', 12);

            $headers = ['Title', 'Year', 'Branch', 'Status'];
            $tableData = [];

            foreach ($data['workplans'] as $workplan) {
                $tableData[] = [
                    $workplan['title'] ?? '',
                    $workplan['year'] ?? '',
                    $workplan['branch_name'] ?? '',
                    ucfirst($workplan['status'] ?? '')
                ];
            }

            $pdf->addTable($headers, $tableData);
        }
    }

    /**
     * Add NASP tables
     *
     * @param PdfHelper $pdf
     * @param array $data
     */
    private function addNaspTables($pdf, $data)
    {
        if (!empty($data['plans'])) {
            $pdf->addSubtitle('NASP Plans', 12);

            $headers = ['Code', 'Title', 'Status'];
            $tableData = [];

            foreach ($data['plans'] as $plan) {
                $tableData[] = [
                    $plan['code'] ?? '',
                    $plan['title'] ?? '',
                    $plan['nasp_status'] == 1 ? 'Active' : 'Inactive'
                ];
            }

            $pdf->addTable($headers, $tableData);
        }
    }

    /**
     * Add MTDP tables
     *
     * @param PdfHelper $pdf
     * @param array $data
     */
    private function addMtdpTables($pdf, $data)
    {
        if (!empty($data['plans'])) {
            $pdf->addSubtitle('MTDP Plans', 12);

            $headers = ['Code', 'Title', 'Status'];
            $tableData = [];

            foreach ($data['plans'] as $plan) {
                $tableData[] = [
                    $plan['code'] ?? '',
                    $plan['title'] ?? '',
                    $plan['mtdp_status'] == 1 ? 'Active' : 'Inactive'
                ];
            }

            $pdf->addTable($headers, $tableData);
        }
    }

    /**
     * Add commodity tables
     *
     * @param PdfHelper $pdf
     * @param array $data
     */
    private function addCommodityTables($pdf, $data)
    {
        if (!empty($data['productions'])) {
            $pdf->addSubtitle('Production Records', 12);

            $headers = ['Commodity', 'Item', 'Quantity', 'Unit', 'Date Range'];
            $tableData = [];

            foreach (array_slice($data['productions'], 0, 50) as $production) { // Limit to 50 records
                $tableData[] = [
                    $production['commodity_name'] ?? '',
                    $production['item'] ?? '',
                    number_format($production['quantity'] ?? 0, 2),
                    $production['unit_of_measurement'] ?? '',
                    date('M d', strtotime($production['date_from'])) . ' - ' . date('M d, Y', strtotime($production['date_to']))
                ];
            }

            $pdf->addTable($headers, $tableData);
        }
    }

    /**
     * Add activity maps tables
     *
     * @param PdfHelper $pdf
     * @param array $data
     */
    private function addActivityMapsTables($pdf, $data)
    {
        if (!empty($data['activities'])) {
            $pdf->addSubtitle('Activities with GPS Coordinates', 12);

            $headers = ['Activity', 'Type', 'Location', 'GPS Coordinates'];
            $tableData = [];

            foreach ($data['activities'] as $activity) {
                if (!empty($activity['coordinates'])) {
                    $tableData[] = [
                        $activity['title'] ?? '',
                        ucfirst($activity['type'] ?? ''),
                        $activity['location'] ?? '',
                        $activity['coordinates'] ?? ''
                    ];
                }
            }

            $pdf->addTable($headers, $tableData);
        }
    }
}
