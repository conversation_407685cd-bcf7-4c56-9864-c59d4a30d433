<?php

namespace App\Controllers;

use App\Helpers\PdfHelper;
use App\Services\PdfService;
use CodeIgniter\Controller;

/**
 * Test PDF Controller
 * 
 * Simple controller to test PDF functionality
 */
class TestPdfController extends Controller
{
    /**
     * Test basic PDF generation
     */
    public function testBasic()
    {
        try {
            $pdf = new PdfHelper('Test PDF Document');
            $pdf->addPage();
            
            $pdf->addTitle('PDF Test Document');
            $pdf->addText('This is a test PDF document generated by the AMIS system.');
            $pdf->addLineBreak(10);
            
            $pdf->addSubtitle('Test Table');
            $headers = ['Name', 'Type', 'Status'];
            $data = [
                ['Test Activity 1', 'Training', 'Active'],
                ['Test Activity 2', 'Infrastructure', 'Pending'],
                ['Test Activity 3', 'Inputs', 'Complete']
            ];
            
            $pdf->addTable($headers, $data);
            
            return $pdf->output('test_basic.pdf', 'I');
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'error' => 'PDF generation failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test activity PDF generation
     */
    public function testActivity($activityId = 1)
    {
        try {
            $pdfService = new PdfService();
            return $pdfService->generateActivityPdf($activityId);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'error' => 'Activity PDF generation failed: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Test report PDF generation
     */
    public function testReport()
    {
        try {
            // Create sample data for testing
            $data = [
                'workplans' => [
                    ['title' => 'Test Workplan 1', 'year' => '2024', 'branch_name' => 'Test Branch', 'status' => 'active'],
                    ['title' => 'Test Workplan 2', 'year' => '2024', 'branch_name' => 'Test Branch 2', 'status' => 'draft']
                ],
                'activities' => [
                    ['title' => 'Test Activity 1', 'activity_type' => 'training', 'workplan_title' => 'Test Workplan 1'],
                    ['title' => 'Test Activity 2', 'activity_type' => 'infrastructure', 'workplan_title' => 'Test Workplan 2']
                ],
                'proposals' => []
            ];
            
            $chartData = [
                'totalCost' => 50000.00,
                'workplanStatusCounts' => ['active' => 1, 'draft' => 1]
            ];
            
            $pdfService = new PdfService();
            return $pdfService->generateReportPdf('workplan', $data, $chartData);
            
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'error' => 'Report PDF generation failed: ' . $e->getMessage()
            ]);
        }
    }
}
