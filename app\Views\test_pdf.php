<?= $this->extend('templates/system_template') ?>

<?= $this->section('title') ?>
PDF Generation Test
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h4 class="card-title">PDF Generation Test Page</h4>
                            <p class="card-text mb-0">Test the JavaScript-based PDF generation functionality</p>
                        </div>
                        <div>
                            <button onclick="AMISPdf.generateReportPDF('Test')" class="btn btn-light">
                                <i class="fas fa-file-pdf me-1"></i> Export PDF
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Content -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Sample Chart</h5>
                </div>
                <div class="card-body">
                    <canvas id="testChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Sample Data Table</h5>
                </div>
                <div class="card-body">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Item</th>
                                <th>Type</th>
                                <th>Status</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Test Activity 1</td>
                                <td><span class="badge bg-info">Training</span></td>
                                <td><span class="badge bg-success">Active</span></td>
                                <td>K 15,000</td>
                            </tr>
                            <tr>
                                <td>Test Activity 2</td>
                                <td><span class="badge bg-warning">Infrastructure</span></td>
                                <td><span class="badge bg-warning">Pending</span></td>
                                <td>K 25,000</td>
                            </tr>
                            <tr>
                                <td>Test Activity 3</td>
                                <td><span class="badge bg-primary">Inputs</span></td>
                                <td><span class="badge bg-success">Complete</span></td>
                                <td>K 8,500</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h5 class="card-title">Total Activities</h5>
                    <p class="card-text fs-4">25</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h5 class="card-title">Completed</h5>
                    <p class="card-text fs-4">18</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h5 class="card-title">Pending</h5>
                    <p class="card-text fs-4">5</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body text-center">
                    <h5 class="card-title">Cancelled</h5>
                    <p class="card-text fs-4">2</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Test Content -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Test Information</h5>
                </div>
                <div class="card-body">
                    <h6>JavaScript PDF Generation Benefits:</h6>
                    <ul>
                        <li><strong>Better Performance:</strong> No server round-trip needed</li>
                        <li><strong>Chart Integration:</strong> Captures existing charts/graphs exactly as displayed</li>
                        <li><strong>Real-time Generation:</strong> Captures current page state with filters applied</li>
                        <li><strong>Reduced Server Load:</strong> Client-side processing</li>
                        <li><strong>Better UX:</strong> No page reload, instant feedback</li>
                        <li><strong>Easier Maintenance:</strong> No complex server-side PDF libraries</li>
                    </ul>
                    
                    <h6 class="mt-4">Test Instructions:</h6>
                    <ol>
                        <li>Click the "Export PDF" button above</li>
                        <li>The PDF should generate and download automatically</li>
                        <li>Verify that charts, tables, and styling are preserved</li>
                        <li>Check that buttons and navigation elements are hidden in PDF</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Create a test chart
const ctx = document.getElementById('testChart').getContext('2d');
const testChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Completed', 'Pending', 'Cancelled'],
        datasets: [{
            data: [18, 5, 2],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#dc3545'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            title: {
                display: true,
                text: 'Activity Status Distribution'
            }
        }
    }
});
</script>
<?= $this->endSection() ?>
